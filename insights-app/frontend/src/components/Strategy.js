import React, { useState, useEffect } from 'react';
import { HashRouter, useParams } from 'react-router-dom';
import axios from 'axios';
import StrategyDropdown from './StrategyDropdown';

const Strategy = () => {
  const { strategyName } = useParams();
  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle');
  const [strategyData, setStrategyData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStrategyData(currentStrategy);
  }, [currentStrategy]);

  const fetchStrategyData = async (strategy) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/strategy/${strategy}/`);
      setStrategyData(response.data);
    } catch (error) {
      console.error('Error fetching strategy data:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="content-page">
      <StrategyDropdown 
        currentStrategy={currentStrategy}
        onStrategyChange={setCurrentStrategy}
      />
      <p className="section_title">Strategy Insights</p>
      <hr></hr>
      {loading ? (
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : (
        <div className="strategy-content">
        
        </div>
      )}
    </div>
  );
};

export default Strategy;
