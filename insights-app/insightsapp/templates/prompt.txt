I have started to build a django web app but now want you to build it. It should have a Django backend and React frontend. On the /strategy page you can see in the screenshot I want a dropdown menu like the one on the screenshot. Instead of the lorem ipsum text I want a chart displaying data. It should have a dropdown for a certain attribute and one for a start month and year and one for an ending month and year with the ending one having a button "now". Then for each month, you will get data in the following json format:

{
    "2025 - 04": {
        "Strategy Name (e.g. Ausgewogen)": {
            "Currency": {
                "USD": 4.234,
                "EUR": 12.92,
                "DKK": 1.22
            },
            ...
        },
        ...
    },
    ...
}

Now lets build the strategy page data visualizations. From data/data_example.json you can load an example of how the data is going to look like.
I want a line chart displaying data on a monthly basis. It should have a dropdown for a certain attribute and one for a start month and year and one for an ending month and year with the ending one having a button "now". The dropdowns for the date range should be at the top right of the chart.
It needs to have a legend. The Y-Axis is in percent and the X-Axis shows the months. 

Here is the data example:
{
    "2025 - 04": {
        "Strategy Name (e.g. Ausgewogen)": {
            "Currency": {
                "USD": 4.234,
                "EUR": 12.92,
                "DKK": 1.22
            }
        }
    }
}

The values are all percentages.
Also add a dropdown for the attribute to be displayed to the left of the date range selector. The dropdown should automatically be adjusted depending on the attributes that are available (here only "Currency" for instance). Also make sure to parse the month correctly, "2025 - 04" is April 2025.
